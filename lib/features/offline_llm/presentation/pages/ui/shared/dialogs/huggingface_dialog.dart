import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/artificial_intelligence.dart';
import 'package:flutter/material.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/huggingface_selection.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/large_language_models/llama_cpp_model.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/shared/dialogs/loading_dialog.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/shared/dropdowns/huggingface_model_dropdown.dart';
import 'package:provider/provider.dart';

class HuggingfaceDialog extends StatefulWidget {
  const HuggingfaceDialog({super.key});

  @override
  State<HuggingfaceDialog> createState() => _HuggingfaceDialogState();
}

class _HuggingfaceDialogState extends State<HuggingfaceDialog> {
  @override
  Widget build(BuildContext context) {
    return Consumer<HuggingfaceSelection>(
      builder: (context, huggingfaceSelection, child) {
        if (huggingfaceSelection.progress != null) {
          return LoadingDialog(
            title: "Downloading Model ${huggingfaceSelection.progress!}/100",
          );
        }

        return AlertDialog(
          title: const Text(
            'Select HuggingFace Model',
            textAlign: TextAlign.center,
          ),
          content: const SizedBox(
            width: double.maxFinite,
            child: HuggingfaceModelDropdown(),
          ),
          actions: [
            buildDeleteButton(huggingfaceSelection),
            buildSelectButton(huggingfaceSelection),
            FilledButton(
              onPressed: () {
                if (mounted) {
                  Navigator.of(context).pop();
                }
              },
              child: const Text("Close"),
            ),
          ],
          actionsAlignment: MainAxisAlignment.center,
        );
      },
    );
  }

  Widget buildDeleteButton(HuggingfaceSelection huggingfaceSelection) {
    return FutureBuilder(
      future: huggingfaceSelection.alreadyExists,
      builder: buildDeleteButtonFuture,
    );
  }

  Widget buildDeleteButtonFuture(
    BuildContext context,
    AsyncSnapshot<bool> snapshot,
  ) {
    if (snapshot.connectionState == ConnectionState.done) {
      if (!(snapshot.data as bool)) {
        return const SizedBox.shrink();
      }

      return FilledButton(
        onPressed: () {
          HuggingfaceSelection.of(context).delete();
          if (mounted) {
            Navigator.of(context).pop();
          }
        },
        child: const Text("Delete"),
      );
    } else {
      return const CircularProgressIndicator();
    }
  }

  Widget buildSelectButton(HuggingfaceSelection huggingfaceSelection) {
    return FutureBuilder(
      future: huggingfaceSelection.alreadyExists,
      builder: buildSelectButtonFuture,
    );
  }

  Widget buildSelectButtonFuture(
    BuildContext context,
    AsyncSnapshot<bool> snapshot,
  ) {
    if (snapshot.connectionState == ConnectionState.done) {
      return FilledButton(
        onPressed: () async {
          try {
            final future = HuggingfaceSelection.of(context).download();
            final (filePath, tag) =
                await future; // Await the future to get the result

            if (mounted) {
              // TODO: need to check, switch model to LlamaCppModel from (if) OllamaModel first
              LlamaCppModel.of(context).setModelWithFuture(
                filePath,
                tag,
              ); // Pass the parameters correctly
              // Update the ArtificialIntelligence instance
              // ArtificialIntelligence.of(context).llm = LlamaCppModel.of(context);
              ArtificialIntelligence.of(context).notify();
              Navigator.of(context).pop();
            }
          } catch (e) {
            // Handle any errors during download/selection
            if (mounted) {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
            }
          }
        },
        child: Text((snapshot.data as bool) ? "Select" : "Download"),
      );
    } else {
      return const CircularProgressIndicator();
    }
  }
}
