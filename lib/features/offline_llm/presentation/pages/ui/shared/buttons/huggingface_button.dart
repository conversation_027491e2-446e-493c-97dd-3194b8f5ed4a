import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:diogeneschatbot/features/offline_llm/presentation/pages/ui/shared/dialogs/huggingface_dialog.dart';

class HuggingfaceButton extends StatelessWidget {
  const HuggingfaceButton({super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      tooltip: 'HuggingFace',
      onPressed: () async {
        // Use a more robust dialog approach
        await showDialog<void>(
          context: context,
          barrierDismissible: true,
          builder: (BuildContext dialogContext) => const HuggingfaceDialog(),
        );
      },
      icon: SizedBox(
        width: 30.0,
        height: 30.0,
        child: SvgPicture.asset('assets/huggingface-colour.svg'),
      ),
    );
  }
}
