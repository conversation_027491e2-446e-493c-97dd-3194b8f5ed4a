import 'package:flutter/material.dart';
import 'package:diogeneschatbot/models/huggingface_model.dart';
import 'package:diogeneschatbot/features/offline_llm/data/local/classes/providers/huggingface_selection.dart';
import 'package:provider/provider.dart';

class HuggingfaceModelDropdown extends StatefulWidget {
  const HuggingfaceModelDropdown({super.key});

  @override
  State<HuggingfaceModelDropdown> createState() =>
      _HuggingfaceModelDropdownState();
}

class _HuggingfaceModelDropdownState extends State<HuggingfaceModelDropdown> {
  List<HuggingfaceModel> options = [];

  @override
  Widget build(BuildContext context) {
    if (options.isEmpty) {
      return buildFuture();
    }

    return buildRow(context);
  }

  Widget buildFuture() {
    return FutureBuilder<List<HuggingfaceModel>>(
      future: HuggingfaceModel.getAll(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Text('Error loading models: ${snapshot.error}');
        }

        if (snapshot.connectionState == ConnectionState.done) {
          if (snapshot.hasData) {
            options = snapshot.data!;
            return buildRow(context);
          } else {
            return const Text('No models available');
          }
        } else {
          return buildLoading();
        }
      },
    );
  }

  Widget buildRow(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [buildModelRow(context), buildTagRow(context)],
    );
  }

  Widget buildModelRow(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(child: buildModelText()),
        const SizedBox(width: 8),
        GestureDetector(
          onTap: () => _showModelSelectionDialog(context),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.arrow_drop_down,
              color: Theme.of(context).colorScheme.onSurface,
              size: 24,
            ),
          ),
        ),
      ],
    );
  }

  Widget buildModelText() {
    return Consumer<HuggingfaceSelection>(
      builder: (context, huggingfaceSelection, child) {
        String text;

        if (huggingfaceSelection.model != null) {
          text = huggingfaceSelection.model!.name;
        } else {
          text = 'Select Model';
        }

        return Text(
          text,
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        );
      },
    );
  }

  void selectModel(HuggingfaceModel model) {
    if (mounted) {
      HuggingfaceSelection.of(context).model = model;
      if (model.tags.isNotEmpty) {
        HuggingfaceSelection.of(context).tag = model.tags.keys.first;
      }
    }
  }

  Widget buildTagRow(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(child: buildTagText()),
        const SizedBox(width: 8),
        GestureDetector(
          onTap: () => _showTagSelectionDialog(context),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.arrow_drop_down,
              color: Theme.of(context).colorScheme.onSurface,
              size: 24,
            ),
          ),
        ),
      ],
    );
  }

  Widget buildTagText() {
    return Consumer<HuggingfaceSelection>(
      builder: (context, huggingfaceSelection, child) {
        String text;

        if (huggingfaceSelection.tag != null) {
          text = huggingfaceSelection.tag!;
        } else {
          text = 'Select Tag';
        }

        return Text(
          text,
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        );
      },
    );
  }

  void selectTag(String tag) {
    if (mounted) {
      HuggingfaceSelection.of(context).tag = tag;
    }
  }

  void _showModelSelectionDialog(BuildContext context) {
    if (!mounted) return;

    showDialog<HuggingfaceModel>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Select Model'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              itemCount: options.length,
              itemBuilder: (context, index) {
                final model = options[index];
                return ListTile(
                  title: Text(model.name),
                  subtitle: Text('${model.family} - ${model.series}'),
                  onTap: () {
                    Navigator.of(dialogContext).pop();
                    selectModel(model);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  void _showTagSelectionDialog(BuildContext context) {
    if (!mounted) return;

    final huggingfaceSelection = HuggingfaceSelection.of(context);
    if (huggingfaceSelection.model == null ||
        huggingfaceSelection.model!.tags.isEmpty) {
      return;
    }

    final tags = huggingfaceSelection.model!.tags.keys.toList();

    showDialog<String>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Select Tag'),
          content: SizedBox(
            width: double.maxFinite,
            height: 200,
            child: ListView.builder(
              itemCount: tags.length,
              itemBuilder: (context, index) {
                final tag = tags[index];
                return ListTile(
                  title: Text(tag),
                  onTap: () {
                    Navigator.of(dialogContext).pop();
                    selectTag(tag);
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  Widget buildLoading() {
    return const SizedBox(
      width: 24,
      height: 24,
      child: Center(child: CircularProgressIndicator(strokeWidth: 3.0)),
    );
  }
}
