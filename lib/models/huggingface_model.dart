import 'dart:convert';

import 'package:flutter/services.dart';

class HuggingfaceModel {
  final String name;
  final String family;
  final String series;
  final String template;
  final String repo;
  final String branch;
  final Map<String, String> tags;

  HuggingfaceModel({
    required this.name,
    required this.family,
    required this.series,
    required this.template,
    required this.repo,
    required this.branch,
    required this.tags,
  });

  factory HuggingfaceModel.fromJson(Map<String, dynamic> json) {
    try {
      // Validate required fields
      if (json['name'] == null ||
          json['family'] == null ||
          json['series'] == null ||
          json['template'] == null ||
          json['repo'] == null ||
          json['branch'] == null) {
        throw Exception('Missing required fields in JSON: $json');
      }

      // Handle tags field safely
      final tagsData = json['tags'];
      Map<String, String> tags = {};
      if (tagsData != null && tagsData is Map) {
        tags = Map<String, String>.from(tagsData);
      }

      return HuggingfaceModel(
        name: json['name'] as String,
        family: json['family'] as String,
        series: json['series'] as String,
        template: json['template'] as String,
        repo: json['repo'] as String,
        branch: json['branch'] as String,
        tags: tags,
      );
    } catch (e) {
      rethrow;
    }
  }

  static Future<List<HuggingfaceModel>> getAll() async {
    try {
      final jsonString = await rootBundle.loadString(
        'assets/huggingface_models.json',
      );

      if (jsonString.isEmpty) {
        return [];
      }

      final List<dynamic> jsonList = json.decode(jsonString);

      // Parse models with individual error handling
      final List<HuggingfaceModel> models = [];
      for (int i = 0; i < jsonList.length; i++) {
        try {
          final model = HuggingfaceModel.fromJson(jsonList[i]);
          models.add(model);
        } catch (e) {
          // Continue with next model instead of failing completely
          // This handles cases where individual models have invalid data
          continue;
        }
      }

      return models;
    } catch (e) {
      // Return empty list if there's any error loading the models
      return [];
    }
  }
}
